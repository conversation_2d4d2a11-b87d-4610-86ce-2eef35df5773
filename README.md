# LeviLauncherDex Builder

This project builds the `launcher.dex` file used by [<PERSON>Launch<PERSON>](https://github.com/LiteLDev/LeviLaunchroid), a third-party Minecraft Pocket Edition launcher. The generated DEX file contains essential Java classes that enable Minecraft launching functionality within the LeviLaunchroid Android application.

## About

LeviLaunchroid is a third-party launcher for Minecraft Pocket Edition that requires specific Java classes to function properly. This project compiles those classes into a DEX file (`launcher.dex`) that gets placed in the LeviLaunchroid app's assets directory (`app/src/main/assets/launcher.dex`).

## Project Structure

```
LeviLauncherDex/
├── src/main/java/com/mojang/minecraftpe/
│   ├── Launcher.java                    # Main launcher class (kept)
│   ├── NotificationListenerService.java # Notification service (kept)
│   └── store/
│       ├── amazonappstore/
│       │   └── AmazonAppStore.java      # Amazon store integration (kept)
│       └── googleplay/
│           └── GooglePlayStore.java     # Google Play integration (kept)
├── build.gradle                        # Gradle build configuration
├── build.bat                          # Windows build script
├── build.sh                           # Linux/macOS build script
├── modify_dex.py                      # Python script to remove unused classes
├── gradle.properties
├── settings.gradle
└── README.md
```

**Note**: Several classes are removed during the build process to optimize the DEX file size. The removed classes include MainActivity, FilePickerManagerHandler, and various store-related classes that are not needed for the core launcher functionality.

## Quick Start

### For LeviLaunchroid Developers

If you just need the `launcher.dex` file for LeviLaunchroid:

1. **Download the latest release** from the [Releases](../../releases) page
2. **Copy `launcher.dex`** to your LeviLaunchroid project: `app/src/main/assets/launcher.dex`
3. **Build your LeviLaunchroid app** as usual

### For Contributors/Developers

If you want to modify the classes or build the DEX file yourself:

## Prerequisites

1. **Java Development Kit (JDK) 21 or higher**
   - Download from: https://adoptium.net/
   - Verify installation: `java -version`

2. **Android SDK**
   - Install Android Studio or standalone SDK tools
   - Set the `ANDROID_HOME` environment variable to your Android SDK path
   - **Windows**: `ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk`
   - **Linux**: `ANDROID_HOME=/home/<USER>/Android/Sdk`
   - **macOS**: `ANDROID_HOME=/Users/<USER>/Library/Android/sdk`

3. **Python 3.6+** (for the DEX modification script)
   - Download from: https://python.org/

## Building the DEX File

### Method 1: Automated Build (Recommended)

This method builds the complete optimized `launcher.dex` file ready for use in LeviLaunchroid:

```bash
# Clone the repository
git clone https://github.com/LiteLDev/LeviLauncherDex.git
cd LeviLauncherDex

# Set your Android SDK path
export ANDROID_HOME=/path/to/your/android/sdk  # Linux/macOS
# OR
set ANDROID_HOME=C:\path\to\your\android\sdk   # Windows

# Build the optimized launcher.dex
./build.sh          # Linux/macOS
# OR
build.bat           # Windows

# The final launcher.dex will be in build/libs/launcher.dex
```

### Method 2: Using Gradle

```bash
# Build using Gradle wrapper
./gradlew createDex     # Linux/macOS
gradlew.bat createDex   # Windows

# Then optimize the DEX file
python modify_dex.py
```

### Output

After building, you'll find:
- **`build/libs/launcher.dex`** - The optimized DEX file for LeviLaunchroid (≈5.8KB)
- **`build/libs/LeviLauncherDex-1.0.jar`** - Intermediate JAR file (≈8.7KB)

## Integration with LeviLaunchroid

### Step 1: Build the DEX file
Follow the build instructions above to generate `launcher.dex`.

### Step 2: Copy to LeviLaunchroid
Copy the generated `launcher.dex` file to your LeviLaunchroid project:

```bash
# Copy launcher.dex to LeviLaunchroid assets
cp build/libs/launcher.dex /path/to/LeviLaunchroid/app/src/main/assets/launcher.dex
```

### Step 3: Build LeviLaunchroid
Build your LeviLaunchroid app as usual. The launcher.dex will be included in the APK and loaded at runtime to provide Minecraft launching capabilities.

## What's in the DEX file?

The optimized `launcher.dex` contains only the essential classes needed for Minecraft launching:

### Included Classes:
- **`com.mojang.minecraftpe.Launcher`** - Main launcher functionality
- **`com.mojang.minecraftpe.NotificationListenerService`** - Notification handling
- **`com.mojang.minecraftpe.store.amazonappstore.AmazonAppStore`** - Amazon App Store integration
- **`com.mojang.minecraftpe.store.googleplay.GooglePlayStore`** - Google Play Store integration

### Removed Classes (for optimization):
- `MainActivity` - Not needed for launcher functionality
- `FilePickerManagerHandler` - File picker interface
- `ExtraLicenseResponseData` - License response data
- `Product`, `Purchase`, `Store`, `StoreListener` - Store-related interfaces

The build process automatically removes these unused classes using the `modify_dex.py` script, reducing the final DEX file size from ~8KB to ~6KB.

## Advanced Usage

### Manual DEX Modification

If you want to customize which classes are included/excluded, you can modify the `modify_dex.py` script:

```python
# Edit the CLASSES_TO_REMOVE list in modify_dex.py
CLASSES_TO_REMOVE = [
    'com/mojang/minecraftpe/MainActivity',
    'com/mojang/minecraftpe/FilePickerManagerHandler',
    # Add or remove classes as needed
]
```

Then run the script manually:
```bash
python modify_dex.py
```

### Verification

To verify your DEX file contents:

```bash
# Check DEX file size and classes (requires Android SDK)
$ANDROID_HOME/build-tools/*/dexdump -f build/libs/launcher.dex | grep "Class descriptor"
```

## Troubleshooting

### Common Issues

1. **ANDROID_HOME not set:**
   - Error: "ANDROID_HOME environment variable is not set"
   - Solution: Set the ANDROID_HOME environment variable to your Android SDK path

2. **Build tools not found:**
   - Error: "dx/d8 tool not found in Android SDK build-tools"
   - Solution: Install Android SDK build-tools through Android Studio SDK Manager

3. **Java compilation errors:**
   - Error: "package android.app does not exist"
   - Solution: Ensure ANDROID_HOME is set and android.jar is accessible

4. **Permission denied (Linux/macOS):**
   - Error: "Permission denied: ./build.sh"
   - Solution: `chmod +x build.sh gradlew`

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes to the Java classes in `src/main/java/`
4. Test the build process: `./build.sh` or `build.bat`
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

This project is part of the LeviLaunchroid ecosystem. Please refer to the main [LeviLaunchroid repository](https://github.com/LiteLDev/LeviLaunchroid) for licensing information.

## Related Projects

- **[LeviLaunchroid](https://github.com/LiteLDev/LeviLaunchroid)** - The main Android launcher application
- **[LiteLoaderBDS](https://github.com/LiteLDev/LiteLoaderBDS)** - Plugin loader for Minecraft Bedrock Dedicated Server
